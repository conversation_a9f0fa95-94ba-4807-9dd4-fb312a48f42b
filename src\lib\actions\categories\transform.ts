// 变换类动作定�?

import { CategoryIds, ActionIds } from "../../constants";
import type { Action } from "../../types/action";
import { applyActionMapping } from "../action-mappings";

// 变换类动作定�?
const rawTransformActions = [
  {
    id: ActionIds.ROTATE_VIDEO,
    nameKey: "actions.rotate_video.name",
    descriptionKey: "actions.rotate_video.description",
    categoryId: CategoryIds.TRANSFORM,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: true,
    order: 10,
    params: [
      {
        key: "angle",
        type: "select",
        nameKey: "actions.rotate_video.params.angle",
        required: true,
        defaultValue: 90,
        options: [
          { value: 90, labelKey: "actions.rotate_video.options.90" },
          { value: 180, labelKey: "actions.rotate_video.options.180" },
          { value: 270, labelKey: "actions.rotate_video.options.270" },
          { value: "custom", labelKey: "actions.rotate_video.options.custom" },
        ],
      },
      {
        key: "customAngle",
        type: "number",
        nameKey: "actions.rotate_video.params.customAngle",
        required: false,
        defaultValue: 0,
        min: -360,
        max: 360,
        step: 1,
        dependsOn: "angle",
      },
    ],
    validate: (params: Record<string, any>) => {
      const errors = [];
      if (params.angle === "custom") {
        if (params.customAngle < -360 || params.customAngle > 360) {
          errors.push("actions.rotate_video.errors.invalid_custom_angle");
        }
      }
      return {
        isValid: errors.length === 0,
        errors,
      };
    },
  },
  {
    id: ActionIds.SCALE_VIDEO,
    nameKey: "actions.scale_video.name",
    descriptionKey: "actions.scale_video.description",
    categoryId: CategoryIds.TRANSFORM,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: true,
    order: 20,
    params: [
      {
        key: "scale",
        type: "range",
        nameKey: "actions.scale_video.params.scale",
        required: true,
        defaultValue: 1.0,
        min: 0.1,
        max: 5.0,
        step: 0.1,
      },
    ],
    validate: (params: Record<string, any>) => {
      const errors = [];
      if (params.scale < 0.1 || params.scale > 5.0) {
        errors.push("actions.scale_video.errors.invalid_scale");
      }
      return {
        isValid: errors.length === 0,
        errors,
      };
    },
  },
  {
    id: ActionIds.FLIP_HORIZONTAL,
    nameKey: "actions.flip_horizontal.name",
    descriptionKey: "actions.flip_horizontal.description",
    categoryId: CategoryIds.TRANSFORM,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: true,
    order: 30,
    params: [],
  },
  {
    id: ActionIds.FLIP_VERTICAL,
    nameKey: "actions.flip_vertical.name",
    descriptionKey: "actions.flip_vertical.description",
    categoryId: CategoryIds.TRANSFORM,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: true,
    order: 40,
    params: [],
  },
];

// 应用映射并导�?
export const transformActions: Action[] =
  rawTransformActions.map(applyActionMapping);
